[package]
name = "codex-common"
version = { workspace = true }
edition = "2021"

[lints]
workspace = true

[dependencies]
clap = { version = "4", features = ["derive", "wrap_help"], optional = true }
codex-core = { path = "../core" }
toml = { version = "0.8", optional = true }
serde = { version = "1", optional = true }

[features]
# Separate feature so that `clap` is not a mandatory dependency.
cli = ["clap", "toml", "serde"]
elapsed = []
