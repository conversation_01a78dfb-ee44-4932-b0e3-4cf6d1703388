#!/usr/bin/env python3
"""
Simple Flask server running on port 3002.
"""
from flask import Flask, request, jsonify

app = Flask(__name__)


@app.route('/', methods=['GET'])
def index():
    return jsonify({"message": "Hello, <PERSON>!"})


@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "ok"}), 200


if __name__ == '__main__':
    # Listen on all interfaces on port 3002
    app.run(host='0.0.0.0', port=3002)
