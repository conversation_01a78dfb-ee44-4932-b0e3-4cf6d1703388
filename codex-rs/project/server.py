#!/usr/bin/env python3
"""
Simple HTTP server running on port 4001.
"""
from http.server import HTTPServer, BaseHTTPRequestHandler

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header("Content-Type", "text/plain")
        self.end_headers()
        self.wfile.write(b"Hello from the server on port 4001!")

def run(server_class=HTTPServer, handler_class=SimpleHandler):
    server_address = ("", 4001)
    httpd = server_class(server_address, handler_class)
    print("Starting HTTP server on port 4001...")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    finally:
        httpd.server_close()
        print("Server closed.")

if __name__ == "__main__":
    run()
