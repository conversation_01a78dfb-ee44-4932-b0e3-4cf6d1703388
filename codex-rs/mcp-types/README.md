# mcp-types

Types for Model Context Protocol. Inspired by https://crates.io/crates/lsp-types.

As documented on https://modelcontextprotocol.io/specification/2025-03-26/basic:

- TypeScript schema is the source of truth: https://github.com/modelcontextprotocol/modelcontextprotocol/blob/main/schema/2025-03-26/schema.ts
- JSON schema is amenable to automated tooling: https://github.com/modelcontextprotocol/modelcontextprotocol/blob/main/schema/2025-03-26/schema.json
