<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>Giao dịch thành công - VCB Digibank</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&amp;display=swap" rel="stylesheet">
    <style>
        body {
            background: radial-gradient(circle at 50% 30%, #e4f5e8 70%, #dff4ff 100%);
            font-family: 'Roboto', Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 390px;
            margin: 30px auto;
            box-shadow: 0 8px 24px #dbe9e5cc;
            background: #fff;
            border-radius: 24px;
            overflow: hidden;
            padding: 0 0 18px 0;
        }
        .header {
            text-align: left;
            padding: 24px 24px 0 24px;
        }
        .vcb {
            color: #00681d;
            font-weight: bold;
            font-size: 2em;
            letter-spacing: 2px;
            display: inline-block;
        }
        .digibank {
            color: #a2c615;
            font-weight: bold;
            font-size: 2em;
            margin-left: 0.25em;
        }
        .circle-success {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        .tick {
            background: #52c41a;
            color: white;
            border-radius: 50%;
            width: 58px;
            height: 58px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.6em;
            box-shadow: 0 2px 8px #8ee39988;
        }
        .success {
            text-align: center;
            font-size: 1.3em;
            font-weight: 700;
            color: #20822d;
            margin-top: 5px;
        }
        .amount {
            text-align: center;
            color: #01421d;
            font-size: 2.2em;
            font-weight: 900;
            margin: 5px 0;
        }
        .vnd {
            font-size: 1em;
            color: #5ea452;
            font-weight: 500;
            margin-left: 5px;
        }
        .datetime {
            text-align: center;
            color: #888;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
        .info {
            margin: 16px 24px 0 24px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 1.1em;
        }
        .detail-title {
            color: #888;
            font-weight: 400;
        }
        .detail-value {
            font-weight: 700;
            color: #111;
            text-align: right;
            word-break: break-all;
        }
        .bank {
            color: #ff9100;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .tpbank-logo {
            width: 19px;
            height: 19px;
            vertical-align: middle;
        }
        .desc {
            color: #20602b;
            word-break: break-word;
        }
        .free {
            color: #11a740;
            font-weight: 700;
        }
        .fast {
            color: #1976d2;
            font-weight: 700;
        }
        .napas247 {
            font-size: 0.98em;
            margin-left: 8px;
            color: #36a935;
            font-weight: 800;
        }
        .save-row {
            margin-top: 15px;
            padding: 0 24px;
            display: flex;
            align-items: center;
            color: #b6b6b6;
            font-size: 1em;
            gap: 7px;
        }
        .toggle {
            display: inline-block;
            width: 36px;
            height: 20px;
            background: #e6e6e6;
            border-radius: 10px;
            position: relative;
        }
        .toggle::before {
            content: '';
            width: 18px;
            height: 18px;
            background: #d8dad9;
            border-radius: 50%;
            position: absolute;
            top: 1px;
            left: 1px;
        }
        /* Responsive */
        @media (max-width: 430px) {
            .container {
                max-width: 100%;
                border-radius: 0;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <span class="vcb">VCB</span><span class="digibank">Digibank</span>
        </div>
        <div class="circle-success">
            <div class="tick">&#10004;</div>
        </div>
        <div class="success">Giao dịch thành công!</div>
        <div class="amount">
            320,000 <span class="vnd">VND</span>
        </div>
        <div class="datetime">
            15:21 Thứ Năm 05/06/2025
        </div>
        <div class="info">
            <div class="detail-row">
                <span class="detail-title">Tài khoản nhận</span>
                <span class="detail-value">***********</span>
            </div>
            <div class="detail-row">
                <span class="detail-title">Tên người nhận</span>
                <span class="detail-value">TO NGOC THANG</span>
            </div>
            <div class="detail-row">
                <span class="detail-title">Ngân hàng nhận</span>
                <span class="detail-value bank">
                    <!-- Bank logo placeholder as a Unicode triangle (you may replace with a real image) -->
                    <svg class="tpbank-logo" viewBox="0 0 24 24"><polygon points="12,2 22,22 2,22" style="fill:#ff9100"/></svg>
                    TPBANK
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-title"></span>
                <span class="detail-value" style="color: #888; font-weight:400;">Ngân hàng Tiên phong</span>
            </div>
            <div class="detail-row">
                <span class="detail-title">Nội dung</span>
                <span class="detail-value desc">NGUYEN SY LUU chuyen tien san cau</span>
            </div>
            <div class="detail-row">
                <span class="detail-title">Phí chuyển tiền</span>
                <span class="detail-value free">Miễn phí</span>
            </div>
            <div class="detail-row">
                <span class="detail-title">Hình thức chuyển</span>
                <span class="detail-value fast">Chuyển tiền nhanh <span class="napas247">napas 247</span></span>
            </div>
            <div class="detail-row">
                <span class="detail-title">Mã giao dịch</span>
                <span class="detail-value" style="color:#00507c;">9743840108</span>
            </div>
        </div>
        <div class="save-row">
            Lưu mẫu chuyển tiền
            <span class="toggle"></span>
        </div>
    </div>
</body>
</html>