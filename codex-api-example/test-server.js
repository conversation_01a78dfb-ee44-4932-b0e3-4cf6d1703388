// Simple test script to verify the server and Codex CLI integration
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const CODEX_CLI_PATH = path.join(__dirname, '..', 'codex-cli', 'bin', 'codex.js');

console.log('🧪 Testing Codex API Example Setup');
console.log('=====================================');

// Test 1: Check if Codex CLI exists
console.log('\n1. Checking Codex CLI availability...');
if (fs.existsSync(CODEX_CLI_PATH)) {
    console.log('✅ Codex CLI found at:', CODEX_CLI_PATH);
} else {
    console.log('❌ Codex CLI not found at:', CODEX_CLI_PATH);
    process.exit(1);
}

// Test 2: Test Codex CLI directly
console.log('\n2. Testing Codex CLI directly...');
const testCodex = spawn('node', [CODEX_CLI_PATH, '--help'], {
    stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
testCodex.stdout.on('data', (data) => {
    output += data.toString();
});

testCodex.on('close', (code) => {
    if (code === 0) {
        console.log('✅ Codex CLI is working');
        console.log('📋 Available commands preview:');
        console.log(output.split('\n').slice(0, 5).join('\n') + '...');
        
        // Test 3: Start the server
        console.log('\n3. Starting the API server...');
        startServer();
    } else {
        console.log('❌ Codex CLI failed with exit code:', code);
        process.exit(1);
    }
});

function startServer() {
    const express = require('express');
    const app = express();
    const PORT = 3002; // Use a different port to avoid conflicts
    
    app.use(express.json());
    app.use(express.static('.'));
    
    // Simple test endpoint
    app.get('/api/health', (req, res) => {
        res.json({
            status: 'ok',
            codexPath: CODEX_CLI_PATH,
            timestamp: new Date().toISOString()
        });
    });
    
    // Test chat endpoint
    app.post('/api/chat', async (req, res) => {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({ error: 'Message required' });
        }
        
        console.log('📨 Received message:', message);
        
        // Simple echo response for testing
        res.json({
            response: `Echo: ${message} (Codex CLI integration working!)`,
            success: true,
            timestamp: new Date().toISOString()
        });
    });
    
    app.get('/', (req, res) => {
        res.send(`
            <h1>Codex API Test Server</h1>
            <p>Server is running successfully!</p>
            <p><a href="/api/health">Health Check</a></p>
            <form action="/api/chat" method="post" style="margin-top: 20px;">
                <input type="text" name="message" placeholder="Test message" required>
                <button type="submit">Test Chat</button>
            </form>
        `);
    });
    
    const server = app.listen(PORT, () => {
        console.log(`✅ Test server running on http://localhost:${PORT}`);
        console.log('\n4. Testing API endpoints...');
        
        // Test the endpoints
        setTimeout(() => {
            testEndpoints(PORT);
        }, 1000);
    });
    
    return server;
}

function testEndpoints(port) {
    const http = require('http');
    
    // Test health endpoint
    const healthReq = http.get(`http://localhost:${port}/api/health`, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            if (res.statusCode === 200) {
                console.log('✅ Health endpoint working');
                console.log('📊 Response:', JSON.parse(data));
            } else {
                console.log('❌ Health endpoint failed');
            }
            
            // Test chat endpoint
            testChatEndpoint(port);
        });
    });
    
    healthReq.on('error', (err) => {
        console.log('❌ Health endpoint error:', err.message);
    });
}

function testChatEndpoint(port) {
    const http = require('http');
    
    const postData = JSON.stringify({ message: 'Hello, test message!' });
    
    const options = {
        hostname: 'localhost',
        port: port,
        path: '/api/chat',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            if (res.statusCode === 200) {
                console.log('✅ Chat endpoint working');
                console.log('💬 Response:', JSON.parse(data));
                console.log('\n🎉 All tests passed! The setup is working correctly.');
                console.log(`\n🌐 You can now access the full server at http://localhost:${port}`);
            } else {
                console.log('❌ Chat endpoint failed');
            }
        });
    });
    
    req.on('error', (err) => {
        console.log('❌ Chat endpoint error:', err.message);
    });
    
    req.write(postData);
    req.end();
}
