const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3002;

// Middleware
app.use(express.json());
app.use(express.static('.'));

// Path to the Codex CLI wrapper
const CODEX_WRAPPER_PATH = path.join(__dirname, 'codex-wrapper.js');
const CODEX_CLI_PATH = path.join(__dirname, '..', 'codex-cli', 'bin', 'codex.js');

// Verify Codex CLI exists
if (!fs.existsSync(CODEX_CLI_PATH)) {
    console.error('Codex CLI not found at:', CODEX_CLI_PATH);
    console.error('Please ensure the codex-cli directory is properly set up.');
    process.exit(1);
}

// Verify wrapper exists
if (!fs.existsSync(CODEX_WRAPPER_PATH)) {
    console.error('Codex wrapper not found at:', CODEX_WRAPPER_PATH);
    process.exit(1);
}

console.log('Using Codex CLI at:', CODEX_CLI_PATH);
console.log('Using wrapper at:', CODEX_WRAPPER_PATH);

/**
 * Execute Codex CLI with the given prompt
 * @param {string} prompt - The user's prompt/message
 * @returns {Promise<{success: boolean, output: string, error?: string}>}
 */
function executeCodex(prompt) {
    return new Promise((resolve) => {
        console.log('Executing Codex with prompt:', prompt);
        
        // Use the wrapper script to handle non-interactive execution
        const codex = spawn('node', [CODEX_WRAPPER_PATH, prompt], {
            stdio: ['ignore', 'pipe', 'pipe'], // Ignore stdin to prevent raw mode issues
            cwd: process.cwd(),
            env: {
                ...process.env,
                // Force non-interactive mode
                CI: '1',
                TERM: 'dumb',
                NODE_ENV: 'production',
                // Disable TTY detection
                FORCE_COLOR: '0',
                NO_COLOR: '1'
            },
            detached: false
        });

        let stdout = '';
        let stderr = '';

        codex.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        codex.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        codex.on('close', (code) => {
            console.log(`Codex process exited with code: ${code}`);
            console.log('STDOUT:', stdout);
            console.log('STDERR:', stderr);
            if (code === 0) {
                resolve({
                    success: true,
                    output: stdout.trim() || 'Command completed successfully.'
                });
            } else {
                resolve({
                    success: false,
                    output: stderr.trim() || stdout.trim() || 'Unknown error occurred.',
                    error: `Process exited with code ${code}`
                });
            }
        });

        codex.on('error', (error) => {
            console.error('Failed to start Codex process:', error);
            resolve({
                success: false,
                output: `Failed to execute Codex: ${error.message}`,
                error: error.message
            });
        });

        // Set a timeout to prevent hanging
        const timeout = setTimeout(() => {
            console.log('Codex process timeout, killing...');
            codex.kill('SIGTERM');
            resolve({
                success: false,
                output: 'Request timed out after 30 seconds.',
                error: 'Timeout'
            });
        }, 30000); // 30 second timeout

        codex.on('close', () => {
            clearTimeout(timeout);
        });
    });
}

// API endpoint for chat
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        console.log('Received chat message:', message);

        if (!message || typeof message !== 'string') {
            return res.status(400).json({
                error: 'Message is required and must be a string'
            });
        }

        console.log('Received chat message:', message);

        // Execute Codex CLI
        const result = await executeCodex(message);
        console.log('Codex result:', result);

        if (result.success) {
            res.json({
                response: result.output,
                success: true
            });
        } else {
            res.json({
                error: result.output,
                success: false
            });
        }
    } catch (error) {
        console.error('Error in chat endpoint:', error);
        res.status(500).json({
            error: 'Internal server error: ' + error.message
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        codexPath: CODEX_CLI_PATH,
        timestamp: new Date().toISOString()
    });
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Codex API Example Server running on http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log(`🤖 Codex CLI path: ${CODEX_CLI_PATH}`);
    console.log('');
    console.log('Available endpoints:');
    console.log(`  GET  /           - Chat interface`);
    console.log(`  POST /api/chat   - Send message to Codex`);
    console.log(`  GET  /api/health - Health check`);
    console.log('');
    console.log('Open your browser and navigate to the URL above to start chatting!');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});