{"name": "codex-api-example", "version": "1.0.0", "description": "Node.js server that provides a chat interface for interacting with the Codex CLI tool", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["codex", "cli", "api", "chat", "ai", "assistant"], "author": "", "license": "ISC", "dependencies": {"express": "^5.1.0"}, "engines": {"node": ">=14.0.0"}}