# Codex API Example

This is a Node.js server that provides a web-based chat interface for interacting with the Codex CLI tool. It demonstrates how to integrate the Codex CLI into a web application using Express.js and child processes.

## Features

- 🌐 **Web Chat Interface**: Beautiful, responsive chat UI for interacting with Codex
- 🤖 **Codex CLI Integration**: Seamlessly executes Codex CLI commands via child processes
- 📡 **REST API**: Simple API endpoint for sending messages to Codex
- ⚡ **Real-time Responses**: Streams responses from the Codex CLI back to the frontend
- 🎨 **Modern UI**: Clean, gradient-based design with message formatting
- 🔄 **Error Handling**: Comprehensive error handling and timeout management

## Prerequisites

- Node.js 14.0.0 or higher
- The Codex CLI must be available in the `../codex-cli/` directory
- Express.js (included in dependencies)

## Installation

1. Navigate to the codex-api-example directory:
   ```bash
   cd codex-api-example
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Ensure the Codex CLI is built and available:
   ```bash
   # The server expects the CLI at ../codex-cli/bin/codex.js
   ls ../codex-cli/bin/codex.js
   ```

## Usage

### Starting the Server

```bash
npm start
```

The server will start on `http://localhost:3000` by default. You can change the port by setting the `PORT` environment variable:

```bash
PORT=8080 npm start
```

### Using the Chat Interface

1. Open your browser and navigate to `http://localhost:3000`
2. Type your message in the input field
3. Click "Send" or press Enter
4. The server will execute the Codex CLI with your message and display the response

### API Endpoints

#### POST /api/chat
Send a message to Codex and get a response.

**Request:**
```json
{
  "message": "Write a Python function to calculate fibonacci numbers"
}
```

**Response:**
```json
{
  "response": "Here's a Python function to calculate Fibonacci numbers:\n\n```python\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n```",
  "success": true
}
```

**Error Response:**
```json
{
  "error": "Error message here",
  "success": false
}
```

#### GET /api/health
Check if the server and Codex CLI are working properly.

**Response:**
```json
{
  "status": "ok",
  "codexPath": "/path/to/codex-cli/bin/codex.js",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Example Usage

### Using curl to interact with the API:

```bash
# Send a message to Codex
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Create a simple HTML page with a button"}'

# Check server health
curl http://localhost:3000/api/health
```

### Using the web interface:

1. Open `http://localhost:3000` in your browser
2. Try these example prompts:
   - "Write a Python script to read a CSV file"
   - "Create a simple Express.js server"
   - "Help me debug this JavaScript function"
   - "Generate a README file for my project"

## Architecture

```
┌─────────────────┐    HTTP     ┌─────────────────┐    spawn()    ┌─────────────────┐
│   Web Browser   │ ──────────► │   Express.js    │ ────────────► │   Codex CLI     │
│   (Frontend)    │             │   Server        │               │   (TypeScript)  │
└─────────────────┘             └─────────────────┘               └─────────────────┘
        │                               │                               │
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────────┐             ┌─────────────────┐               ┌─────────────────┐
│   index.html    │             │   server.js     │               │   AI Model      │
│   - Chat UI     │             │   - API routes  │               │   - Processing  │
│   - JavaScript  │             │   - Process mgmt│               │   - Responses   │
└─────────────────┘             └─────────────────┘               └─────────────────┘
```

## Configuration

The server can be configured using environment variables:

- `PORT`: Server port (default: 3000)
- `CI`: Set to '1' to ensure non-interactive mode
- `TERM`: Terminal type (set to 'dumb' for non-interactive)

## Troubleshooting

### Common Issues

1. **"Codex CLI not found"**: Ensure the Codex CLI is built and available at `../codex-cli/bin/codex.js`

2. **"Request timed out"**: The server has a 30-second timeout for Codex responses. Complex requests may need more time.

3. **"Process exited with code 1"**: Check the server logs for detailed error messages from the Codex CLI.

### Debug Mode

To see detailed logs, check the server console output. All Codex CLI interactions are logged including:
- Input prompts
- Process exit codes
- STDOUT and STDERR output

## Security Considerations

⚠️ **Important**: This example is for demonstration purposes. In production:

- Add authentication and authorization
- Implement rate limiting
- Sanitize user inputs
- Use HTTPS
- Add proper logging and monitoring
- Consider sandboxing the Codex CLI execution

## License

This example is provided as-is for educational and demonstration purposes.
