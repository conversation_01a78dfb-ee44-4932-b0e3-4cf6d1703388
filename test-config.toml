# Codex-rs Configuration with Custom MCP Tools

# Custom MCP Tools Server
[mcp_servers.custom_tools]
command = "python3"
args = ["/home/<USER>/Documents/AI/codex/my-custom-mcp-server/server.py"]

# Basic sandbox permissions for testing
sandbox_permissions = [
    "disk-full-read-access",
    "disk-write-cwd",
    "disk-write-platform-user-temp-folder"
]

# Auto-approval for testing (be careful in production!)
approval_policy = "never"
