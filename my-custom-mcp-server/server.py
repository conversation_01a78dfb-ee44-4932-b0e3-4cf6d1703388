#!/usr/bin/env python3
"""
Custom MCP Server Example
Provides custom tools for codex-rs integration
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional
import argparse
import logging

# MCP Server implementation
class MCPServer:
    def __init__(self, name: str):
        self.name = name
        self.tools = {}
        self.resources = {}
        self.initialized = False

    def add_tool(self, name: str, description: str, parameters: Dict[str, Any], handler):
        """Add a tool to the server"""
        self.tools[name] = {
            "name": name,
            "description": description,
            "inputSchema": {
                "type": "object",
                "properties": parameters,
                "required": list(parameters.keys()) if parameters else []
            },
            "handler": handler
        }

    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming MCP requests"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")

        try:
            if method == "initialize":
                self.initialized = True
                result = {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "serverInfo": {
                        "name": self.name,
                        "version": "1.0.0"
                    }
                }

            elif method == "initialized":
                # Notification - no response needed
                return None

            elif method == "tools/list":
                if not self.initialized:
                    raise Exception("Server not initialized")

                result = {
                    "tools": [
                        {
                            "name": tool["name"],
                            "description": tool["description"],
                            "inputSchema": tool["inputSchema"]
                        }
                        for tool in self.tools.values()
                    ]
                }

            elif method == "tools/call":
                if not self.initialized:
                    raise Exception("Server not initialized")

                tool_name = params.get("name")
                arguments = params.get("arguments", {})

                if tool_name in self.tools:
                    try:
                        tool_result = await self.tools[tool_name]["handler"](arguments)
                        result = {
                            "content": [
                                {
                                    "type": "text",
                                    "text": str(tool_result)
                                }
                            ]
                        }
                    except Exception as e:
                        result = {
                            "content": [
                                {
                                    "type": "text",
                                    "text": f"Error executing {tool_name}: {str(e)}"
                                }
                            ],
                            "isError": True
                        }
                else:
                    result = {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Unknown tool: {tool_name}"
                            }
                        ],
                        "isError": True
                    }

            else:
                raise Exception(f"Method not found: {method}")

            # Build response
            response = {"jsonrpc": "2.0"}
            if request_id is not None:
                response["id"] = request_id
            if result is not None:
                response["result"] = result

            return response

        except Exception as e:
            # Error response
            response = {"jsonrpc": "2.0"}
            if request_id is not None:
                response["id"] = request_id
            response["error"] = {
                "code": -32603,
                "message": str(e)
            }
            return response

# Custom tool implementations
async def calculator_tool(args: Dict[str, Any]) -> str:
    """Advanced calculator with multiple operations"""
    expression = args.get("expression", "")
    operation = args.get("operation", "eval")
    
    try:
        if operation == "eval":
            # Safe evaluation (limited for security)
            allowed_names = {
                k: v for k, v in __builtins__.items() 
                if k in ['abs', 'round', 'min', 'max', 'sum', 'len']
            }
            allowed_names.update({
                'pow': pow, 'sqrt': lambda x: x**0.5,
                'pi': 3.14159265359, 'e': 2.71828182846
            })
            
            result = eval(expression, {"__builtins__": {}}, allowed_names)
            return f"Result: {result}"
            
        elif operation == "factorial":
            n = int(args.get("number", 0))
            if n < 0:
                return "Factorial is not defined for negative numbers"
            result = 1
            for i in range(1, n + 1):
                result *= i
            return f"Factorial of {n} is {result}"
            
        elif operation == "fibonacci":
            n = int(args.get("number", 0))
            if n <= 0:
                return "Fibonacci sequence starts from 1"
            a, b = 0, 1
            sequence = []
            for _ in range(n):
                sequence.append(a)
                a, b = b, a + b
            return f"First {n} Fibonacci numbers: {sequence}"
            
    except Exception as e:
        return f"Calculation error: {str(e)}"

async def text_processor_tool(args: Dict[str, Any]) -> str:
    """Text processing utilities"""
    text = args.get("text", "")
    operation = args.get("operation", "count_words")
    
    if operation == "count_words":
        words = len(text.split())
        chars = len(text)
        lines = len(text.split('\n'))
        return f"Words: {words}, Characters: {chars}, Lines: {lines}"
        
    elif operation == "reverse":
        return f"Reversed text: {text[::-1]}"
        
    elif operation == "uppercase":
        return f"Uppercase: {text.upper()}"
        
    elif operation == "lowercase":
        return f"Lowercase: {text.lower()}"
        
    elif operation == "title_case":
        return f"Title case: {text.title()}"
        
    elif operation == "word_frequency":
        words = text.lower().split()
        freq = {}
        for word in words:
            freq[word] = freq.get(word, 0) + 1
        sorted_freq = sorted(freq.items(), key=lambda x: x[1], reverse=True)
        return f"Word frequency: {dict(sorted_freq[:10])}"  # Top 10
        
    return f"Unknown text operation: {operation}"

async def file_info_tool(args: Dict[str, Any]) -> str:
    """Get information about files (safe operations only)"""
    import os
    from pathlib import Path
    
    path = args.get("path", ".")
    operation = args.get("operation", "list")
    
    try:
        path_obj = Path(path)
        
        if operation == "list":
            if path_obj.is_dir():
                files = list(path_obj.iterdir())
                file_info = []
                for f in files[:20]:  # Limit to 20 files
                    size = f.stat().st_size if f.is_file() else 0
                    file_info.append(f"{f.name} ({'dir' if f.is_dir() else f'{size} bytes'})")
                return f"Contents of {path}:\n" + "\n".join(file_info)
            else:
                return f"{path} is not a directory"
                
        elif operation == "info":
            if path_obj.exists():
                stat = path_obj.stat()
                return f"""File info for {path}:
- Type: {'Directory' if path_obj.is_dir() else 'File'}
- Size: {stat.st_size} bytes
- Modified: {stat.st_mtime}
- Readable: {os.access(path_obj, os.R_OK)}
- Writable: {os.access(path_obj, os.W_OK)}"""
            else:
                return f"Path {path} does not exist"
                
    except Exception as e:
        return f"File operation error: {str(e)}"

async def system_info_tool(args: Dict[str, Any]) -> str:
    """Get system information"""
    import platform
    import psutil
    import datetime
    
    info_type = args.get("type", "basic")
    
    try:
        if info_type == "basic":
            return f"""System Information:
- OS: {platform.system()} {platform.release()}
- Python: {platform.python_version()}
- Architecture: {platform.machine()}
- Processor: {platform.processor()}
- Hostname: {platform.node()}"""
            
        elif info_type == "memory":
            memory = psutil.virtual_memory()
            return f"""Memory Information:
- Total: {memory.total // (1024**3)} GB
- Available: {memory.available // (1024**3)} GB
- Used: {memory.used // (1024**3)} GB
- Percentage: {memory.percent}%"""
            
        elif info_type == "disk":
            disk = psutil.disk_usage('/')
            return f"""Disk Information:
- Total: {disk.total // (1024**3)} GB
- Used: {disk.used // (1024**3)} GB
- Free: {disk.free // (1024**3)} GB
- Percentage: {(disk.used/disk.total)*100:.1f}%"""
            
        elif info_type == "time":
            now = datetime.datetime.now()
            return f"""Time Information:
- Current time: {now.strftime('%Y-%m-%d %H:%M:%S')}
- Timezone: {now.astimezone().tzname()}
- Timestamp: {now.timestamp()}"""
            
    except Exception as e:
        return f"System info error: {str(e)}"

async def main():
    """Main server loop"""
    parser = argparse.ArgumentParser(description="Custom MCP Server")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    args = parser.parse_args()
    
    if args.debug:
        logging.basicConfig(level=logging.DEBUG)
    
    # Create server instance
    server = MCPServer("custom-tools")
    
    # Register tools
    server.add_tool(
        "calculator",
        "Advanced calculator with multiple mathematical operations",
        {
            "expression": {"type": "string", "description": "Mathematical expression to evaluate"},
            "operation": {"type": "string", "enum": ["eval", "factorial", "fibonacci"], "description": "Type of calculation"},
            "number": {"type": "integer", "description": "Number for factorial/fibonacci operations"}
        },
        calculator_tool
    )
    
    server.add_tool(
        "text_processor", 
        "Process and analyze text in various ways",
        {
            "text": {"type": "string", "description": "Text to process"},
            "operation": {"type": "string", "enum": ["count_words", "reverse", "uppercase", "lowercase", "title_case", "word_frequency"], "description": "Text operation to perform"}
        },
        text_processor_tool
    )
    
    server.add_tool(
        "file_info",
        "Get information about files and directories (read-only)",
        {
            "path": {"type": "string", "description": "File or directory path"},
            "operation": {"type": "string", "enum": ["list", "info"], "description": "Information type to retrieve"}
        },
        file_info_tool
    )
    
    server.add_tool(
        "system_info",
        "Get system information and statistics", 
        {
            "type": {"type": "string", "enum": ["basic", "memory", "disk", "time"], "description": "Type of system information"}
        },
        system_info_tool
    )
    
    # Main communication loop
    try:
        while True:
            line = sys.stdin.readline()
            if not line:
                break
                
            try:
                request = json.loads(line.strip())
                response = await server.handle_request(request)
                
                # Add request ID if present
                if "id" in request:
                    response["id"] = request["id"]
                    
                print(json.dumps(response))
                sys.stdout.flush()
                
            except json.JSONDecodeError:
                error_response = {
                    "error": {
                        "code": -32700,
                        "message": "Parse error"
                    }
                }
                print(json.dumps(error_response))
                sys.stdout.flush()
                
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    asyncio.run(main())
