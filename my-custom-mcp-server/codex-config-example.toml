# Add this to your ~/.codex/config.toml file

# Custom MCP Tools Server
[mcp_servers.custom_tools]
command = "python3"
args = ["/home/<USER>/Documents/AI/codex/my-custom-mcp-server/server.py"]
# Optional: Enable debug mode
# env = { DEBUG = "1" }

# You can also add other MCP servers alongside your custom one:

# File system operations
[mcp_servers.filesystem]
command = "npx"
args = ["@modelcontextprotocol/server-filesystem", "/home/<USER>/Documents"]

# Memory/knowledge management
[mcp_servers.memory]
command = "npx"
args = ["@modelcontextprotocol/server-memory"]

# Web fetching
[mcp_servers.fetch]
command = "npx"
args = ["@modelcontextprotocol/server-fetch"]

# Example full-auto configuration
sandbox_permissions = [
    "disk-full-read-access",
    "disk-write-cwd",
    "disk-write-platform-user-temp-folder",
    "network-full-access"
]
